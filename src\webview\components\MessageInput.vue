<template>
    <div class="message-input-container">
        <!-- 输入框 -->
        <div class="input-wrapper">
            <vscode-textarea
                ref="textareaRef"
                v-model="inputValue"
                placeholder="输入消息... (Shift+Enter 换行，Enter 发送)"
                :disabled="disabled"
                :rows="rows"
                @keydown="handleKeydown"
                @input="handleInput"
                class="message-textarea"
            />

            <!-- 字符计数 -->
            <div class="char-count" :class="{ 'over-limit': isOverLimit }">
                {{ inputValue.length }}/{{ maxLength }}
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="input-actions">
            <!-- 发送按钮 -->
            <vscode-button
                appearance="primary"
                :disabled="!canSend"
                @click="handleSend"
                title="发送消息 (Enter)"
            >
                <vscode-icon name="send" slot="start"></vscode-icon>
                发送
            </vscode-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';

// 导入vscode-elements组件
import '@vscode-elements/elements/dist/vscode-textarea';
import '@vscode-elements/elements/dist/vscode-button';
import '@vscode-elements/elements/dist/vscode-icon';

interface Props {
    disabled?: boolean;
}

interface Emits {
    (e: 'send-message', content: string): void;
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const inputValue = ref('');
const textareaRef = ref<HTMLElement>();
const maxLength = 2000;

// 快速操作选项
const quickActions = [
    { text: '解释代码', tooltip: '请AI解释选中的代码' },
    { text: '优化代码', tooltip: '请AI优化代码结构' },
    { text: '查找问题', tooltip: '请AI帮助查找代码问题' },
    { text: '写注释', tooltip: '请AI为代码添加注释' }
];

// 计算属性
const rows = computed(() => {
    const lines = inputValue.value.split('\n').length;
    return Math.min(Math.max(lines, 1), 6); // 最少1行，最多6行
});

const isOverLimit = computed(() => inputValue.value.length > maxLength);

const canSend = computed(() => {
    return (
        inputValue.value.trim().length > 0 &&
        !props.disabled &&
        !isOverLimit.value
    );
});

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
        if (event.shiftKey) {
            // Shift+Enter 换行，不做处理
            return;
        } else {
            // Enter 发送消息
            event.preventDefault();
            handleSend();
        }
    }
};

// 处理输入事件
const handleInput = () => {
    // 限制字符长度
    if (inputValue.value.length > maxLength) {
        inputValue.value = inputValue.value.substring(0, maxLength);
    }
};

// 发送消息
const handleSend = () => {
    if (!canSend.value) return;

    const content = inputValue.value.trim();
    if (content) {
        emit('send-message', content);
        inputValue.value = '';

        // 重新聚焦到输入框
        nextTick(() => {
            if (textareaRef.value) {
                (textareaRef.value as any).focus();
            }
        });
    }
};

// 插入快速操作文本
const insertQuickAction = (text: string) => {
    inputValue.value = text;
    nextTick(() => {
        if (textareaRef.value) {
            (textareaRef.value as any).focus();
        }
    });
};
</script>

<style scoped>
.message-input-container {
    padding: 16px;
    border-top: 1px solid var(--vscode-panel-border);
    background: var(--vscode-panel-background);
}

.input-wrapper {
    position: relative;
    margin-bottom: 12px;
}

.message-textarea {
    width: 100%;
    resize: none;
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
}

.char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 0.75em;
    color: var(--vscode-descriptionForeground);
    background: var(--vscode-input-background);
    padding: 2px 4px;
    border-radius: 2px;
}

.char-count.over-limit {
    color: var(--vscode-errorForeground);
}

.input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-actions vscode-button {
    font-size: 0.8em;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .input-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .quick-actions {
        justify-content: center;
    }
}
</style>
