import * as vscode from 'vscode';
import {
    ChatMessage,
    ChatSession,
    ChatHistory,
    ChatConfig,
    ChatServiceConfig,
    MessageType,
    ChatStats
} from '../types/chat';
import { AICompletionService } from './aiCompletionService';
import { Logger } from '../utils/logger';

/**
 * 聊天服务
 * 负责管理聊天会话、历史记录和与 AI 的交互
 */
export class ChatService {
    private aiService: AICompletionService;
    private config: ChatServiceConfig;
    private history: ChatHistory;
    private context: vscode.ExtensionContext;

    constructor(
        aiService: AICompletionService,
        context: vscode.ExtensionContext,
        config?: Partial<ChatServiceConfig>
    ) {
        this.aiService = aiService;
        this.context = context;
        this.config = {
            defaultChatConfig: {
                model: 'gpt-3.5-turbo',
                maxContextLength: 4000,
                temperature: 0.7,
                systemPrompt: '你是一个有用的AI助手，专门帮助开发者解决编程问题。',
                streamResponse: false,
                timeout: 30000
            },
            historyConfig: {
                maxSessions: 50,
                maxMessagesPerSession: 100,
                autoSave: true
            },
            storageConfig: {
                keyPrefix: 'ai-chat',
                persistent: true
            },
            ...config
        };

        this.history = this.loadHistory();
        Logger.info('ChatService 初始化完成', this.config);
    }

    /**
     * 创建新的聊天会话
     * @param title 会话标题
     * @returns 新创建的会话
     */
    createNewSession(title?: string): ChatSession {
        Logger.methodCall('ChatService', 'createNewSession');

        const session: ChatSession = {
            id: this.generateId(),
            title: title || `聊天 ${new Date().toLocaleString()}`,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            config: { ...this.config.defaultChatConfig }
        };

        this.history.sessions.unshift(session);
        this.history.activeSessionId = session.id;

        // 限制会话数量
        if (this.history.sessions.length > this.config.historyConfig.maxSessions) {
            this.history.sessions = this.history.sessions.slice(0, this.config.historyConfig.maxSessions);
        }

        this.saveHistory();
        Logger.success(`新会话已创建: ${session.title}`);

        return session;
    }

    /**
     * 获取当前活跃会话
     * @returns 当前会话或新创建的会话
     */
    getCurrentSession(): ChatSession {
        if (!this.history.activeSessionId) {
            return this.createNewSession();
        }

        const session = this.history.sessions.find(s => s.id === this.history.activeSessionId);
        if (!session) {
            return this.createNewSession();
        }

        return session;
    }

    /**
     * 发送消息并获取 AI 响应
     * @param content 用户消息内容
     * @returns AI 响应消息
     */
    async sendMessage(content: string): Promise<ChatMessage> {
        Logger.methodCall('ChatService', 'sendMessage');

        const session = this.getCurrentSession();
        const startTime = Date.now();

        // 创建用户消息
        const userMessage: ChatMessage = {
            id: this.generateId(),
            type: MessageType.USER,
            content: content.trim(),
            timestamp: new Date()
        };

        // 添加用户消息到会话
        session.messages.push(userMessage);
        this.limitSessionMessages(session);

        try {
            // 构建上下文
            const context = this.buildContext(session);

            // 调用 AI 服务
            const response = await this.aiService.getCompletion(context);
            const duration = Date.now() - startTime;

            if (!response.success) {
                throw new Error(response.error || 'AI 服务调用失败');
            }

            // 创建 AI 响应消息
            const assistantMessage: ChatMessage = {
                id: this.generateId(),
                type: MessageType.ASSISTANT,
                content: response.completion,
                timestamp: new Date(),
                metadata: {
                    model: session.config?.model,
                    duration
                }
            };

            // 添加 AI 响应到会话
            session.messages.push(assistantMessage);
            session.updatedAt = new Date();

            this.saveHistory();
            Logger.success(`AI 响应已生成，耗时: ${duration}ms`);

            return assistantMessage;

        } catch (error) {
            const errorMessage: ChatMessage = {
                id: this.generateId(),
                type: MessageType.ERROR,
                content: `抱歉，处理您的请求时出现错误: ${error instanceof Error ? error.message : '未知错误'}`,
                timestamp: new Date(),
                metadata: {
                    error: error instanceof Error ? error.message : '未知错误'
                }
            };

            session.messages.push(errorMessage);
            this.saveHistory();
            Logger.error('发送消息时出现错误', error as Error);

            return errorMessage;
        }
    }

    /**
     * 清空当前会话
     */
    clearCurrentSession(): void {
        Logger.methodCall('ChatService', 'clearCurrentSession');

        const session = this.getCurrentSession();
        session.messages = [];
        session.updatedAt = new Date();

        this.saveHistory();
        Logger.success('当前会话已清空');
    }

    /**
     * 删除会话
     * @param sessionId 会话ID
     */
    deleteSession(sessionId: string): void {
        Logger.methodCall('ChatService', 'deleteSession');

        this.history.sessions = this.history.sessions.filter(s => s.id !== sessionId);

        if (this.history.activeSessionId === sessionId) {
            this.history.activeSessionId = this.history.sessions[0]?.id;
        }

        this.saveHistory();
        Logger.success(`会话已删除: ${sessionId}`);
    }

    /**
     * 导出聊天记录
     * @param sessionId 会话ID，不提供则导出当前会话
     * @returns 导出的文本内容
     */
    exportChat(sessionId?: string): string {
        Logger.methodCall('ChatService', 'exportChat');

        const session = sessionId
            ? this.history.sessions.find(s => s.id === sessionId)
            : this.getCurrentSession();

        if (!session) {
            throw new Error('会话不存在');
        }

        const lines = [
            `# ${session.title}`,
            `导出时间: ${new Date().toLocaleString()}`,
            `会话创建时间: ${session.createdAt.toLocaleString()}`,
            `消息数量: ${session.messages.length}`,
            '',
            '---',
            ''
        ];

        session.messages.forEach(message => {
            const time = message.timestamp.toLocaleTimeString();
            const type = message.type === MessageType.USER ? '👤 用户' :
                message.type === MessageType.ASSISTANT ? '🤖 助手' :
                    message.type === MessageType.ERROR ? '❌ 错误' : '🔧 系统';

            lines.push(`## ${type} (${time})`);
            lines.push('');
            lines.push(message.content);
            lines.push('');

            if (message.metadata?.duration) {
                lines.push(`*响应时间: ${message.metadata.duration}ms*`);
                lines.push('');
            }
        });

        const exportContent = lines.join('\n');
        Logger.success(`聊天记录已导出，共 ${session.messages.length} 条消息`);

        return exportContent;
    }

    /**
     * 获取聊天历史
     * @returns 聊天历史
     */
    getHistory(): ChatHistory {
        return { ...this.history };
    }

    /**
     * 获取聊天统计信息
     * @returns 统计信息
     */
    getStats(): ChatStats {
        const totalMessages = this.history.sessions.reduce((sum, session) => sum + session.messages.length, 0);
        const today = new Date().toDateString();
        const todayMessages = this.history.sessions.reduce((sum, session) => {
            return sum + session.messages.filter(msg => msg.timestamp.toDateString() === today).length;
        }, 0);

        const responseTimes = this.history.sessions
            .flatMap(session => session.messages)
            .filter(msg => msg.metadata?.duration)
            .map(msg => msg.metadata!.duration!);

        const averageResponseTime = responseTimes.length > 0
            ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
            : 0;

        return {
            totalSessions: this.history.sessions.length,
            totalMessages,
            todayMessages,
            averageResponseTime: Math.round(averageResponseTime),
            mostUsedFeatures: ['聊天', '代码生成', '问题解答'] // 简化实现
        };
    }

    /**
     * 构建聊天上下文
     * @param session 会话
     * @returns 上下文字符串
     */
    private buildContext(session: ChatSession): string {
        const systemPrompt = session.config?.systemPrompt || this.config.defaultChatConfig.systemPrompt;
        const maxLength = session.config?.maxContextLength || this.config.defaultChatConfig.maxContextLength;

        let context = systemPrompt + '\n\n';
        const recentMessages = session.messages.slice(-10); // 最近10条消息

        for (const message of recentMessages) {
            if (message.type === MessageType.USER) {
                context += `用户: ${message.content}\n`;
            } else if (message.type === MessageType.ASSISTANT) {
                context += `助手: ${message.content}\n`;
            }
        }

        // 限制上下文长度
        if (context.length > maxLength) {
            context = context.substring(context.length - maxLength);
        }

        return context;
    }

    /**
     * 限制会话消息数量
     * @param session 会话
     */
    private limitSessionMessages(session: ChatSession): void {
        const maxMessages = this.config.historyConfig.maxMessagesPerSession;
        if (session.messages.length > maxMessages) {
            session.messages = session.messages.slice(-maxMessages);
        }
    }

    /**
     * 加载聊天历史
     * @returns 聊天历史
     */
    private loadHistory(): ChatHistory {
        try {
            const stored = this.context.globalState.get<ChatHistory>(`${this.config.storageConfig.keyPrefix}-history`);
            if (stored) {
                // 转换日期字符串为 Date 对象
                stored.sessions.forEach(session => {
                    session.createdAt = new Date(session.createdAt);
                    session.updatedAt = new Date(session.updatedAt);
                    session.messages.forEach(message => {
                        message.timestamp = new Date(message.timestamp);
                    });
                });
                Logger.debug(`已加载 ${stored.sessions.length} 个聊天会话`);
                return stored;
            }
        } catch (error) {
            Logger.error('加载聊天历史失败', error as Error);
        }

        return {
            sessions: [],
            maxSessions: this.config.historyConfig.maxSessions
        };
    }

    /**
     * 保存聊天历史
     */
    private saveHistory(): void {
        if (!this.config.historyConfig.autoSave) {
            return;
        }

        try {
            this.context.globalState.update(`${this.config.storageConfig.keyPrefix}-history`, this.history);
            Logger.debug('聊天历史已保存');
        } catch (error) {
            Logger.error('保存聊天历史失败', error as Error);
        }
    }

    /**
     * 生成唯一ID
     * @returns 唯一ID
     */
    private generateId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
