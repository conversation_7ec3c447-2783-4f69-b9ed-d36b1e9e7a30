// 聊天状态管理服务
import { reactive, ref } from 'vue';
import { messageService } from './vscode';
import type { Message, ChatState } from '../types';

class ChatService {
  // 响应式状态
  public state = reactive<ChatState>({
    messages: [],
    isGenerating: false,
    inputValue: ''
  });

  // 私有状态
  private messageIdCounter = 0;

  constructor() {
    this.setupMessageHandlers();
    this.restoreState();
  }

  // 设置消息处理器
  private setupMessageHandlers() {
    // 监听来自扩展的消息
    messageService.on('addMessage', (data: any) => {
      this.addMessage(data.type, data.content, data.timestamp);
      this.setGenerating(false);
    });

    messageService.on('clearMessages', () => {
      this.clearMessages();
    });

    messageService.on('error', (data: any) => {
      this.addMessage('error', data.content);
      this.setGenerating(false);
    });

    // 监听状态变化事件
    messageService.on('setGenerating', (isGenerating: boolean) => {
      this.setGenerating(isGenerating);
    });
  }

  // 发送消息
  sendMessage(content: string) {
    if (!content.trim() || this.state.isGenerating) {
      return;
    }

    // 添加用户消息到界面
    this.addMessage('user', content.trim());

    // 发送消息到扩展
    messageService.send('sendMessage', { content: content.trim() });

    // 设置生成状态
    this.setGenerating(true);

    // 保存状态
    this.saveState();
  }

  // 添加消息
  addMessage(type: 'user' | 'assistant' | 'error', content: string, timestamp?: number) {
    const message: Message = {
      id: this.generateMessageId(),
      type,
      content,
      timestamp: timestamp || Date.now()
    };

    this.state.messages.push(message);
    this.saveState();
  }

  // 清空消息
  clearMessages() {
    this.state.messages = [];
    this.setGenerating(false);
    this.saveState();
  }

  // 设置生成状态
  setGenerating(isGenerating: boolean) {
    this.state.isGenerating = isGenerating;
    this.saveState();
  }

  // 生成消息ID
  private generateMessageId(): string {
    return `msg_${Date.now()}_${++this.messageIdCounter}`;
  }

  // 保存状态到VSCode
  private saveState() {
    try {
      messageService.send('saveState', {
        messages: this.state.messages,
        isGenerating: this.state.isGenerating
      });
    } catch (error) {
      console.warn('Failed to save state:', error);
    }
  }

  // 恢复状态
  private restoreState() {
    try {
      // 请求恢复状态
      messageService.send('restoreState');
    } catch (error) {
      console.warn('Failed to restore state:', error);
    }
  }

  // 重新发送最后一条消息
  resendLastMessage() {
    const lastUserMessage = [...this.state.messages]
      .reverse()
      .find(msg => msg.type === 'user');

    if (lastUserMessage && !this.state.isGenerating) {
      this.sendMessage(lastUserMessage.content);
    }
  }

  // 删除消息
  deleteMessage(messageId: string) {
    const index = this.state.messages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      this.state.messages.splice(index, 1);
      this.saveState();
    }
  }

  // 编辑消息
  editMessage(messageId: string, newContent: string) {
    const message = this.state.messages.find(msg => msg.id === messageId);
    if (message && message.type === 'user') {
      message.content = newContent;
      this.saveState();
    }
  }

  // 获取消息统计
  getMessageStats() {
    const stats = {
      total: this.state.messages.length,
      user: 0,
      assistant: 0,
      error: 0
    };

    this.state.messages.forEach(msg => {
      stats[msg.type]++;
    });

    return stats;
  }

  // 导出聊天记录
  exportChat() {
    const chatData = {
      timestamp: new Date().toISOString(),
      messages: this.state.messages,
      stats: this.getMessageStats()
    };

    return JSON.stringify(chatData, null, 2);
  }

  // 销毁服务
  destroy() {
    // 清理消息监听器
    messageService.off('addMessage', this.setupMessageHandlers);
    messageService.off('clearMessages', this.setupMessageHandlers);
    messageService.off('error', this.setupMessageHandlers);
    messageService.off('setGenerating', this.setupMessageHandlers);
  }
}

// 创建全局聊天服务实例
export const chatService = new ChatService();
export default chatService;
