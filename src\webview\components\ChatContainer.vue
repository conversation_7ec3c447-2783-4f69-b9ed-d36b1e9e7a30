<template>
    <div class="chat-container">
        <!-- 消息列表区域 -->
        <MessageList
            :messages="state.messages"
            :is-generating="state.isGenerating"
            class="messages-container"
        />

        <!-- 输入框区域 -->
        <MessageInput
            :disabled="state.isGenerating"
            @send-message="handleSendMessage"
            class="input-container"
        />
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import MessageList from './MessageList.vue';
import MessageInput from './MessageInput.vue';
import { chatService } from '../utils/chatService';
import { messageService } from '../utils/vscode';

// 使用聊天服务的响应式状态
const { state } = chatService;

// 处理发送消息
const handleSendMessage = (content: string) => {
    chatService.sendMessage(content);
};

// 组件挂载时初始化
onMounted(() => {
    // 通知扩展 webview 已准备就绪
    messageService.send('ready');
});

// 组件卸载时清理
onUnmounted(() => {
    // 可以在这里进行清理工作，但通常不需要销毁全局服务
});
</script>

<style scoped>
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
}

.messages-container {
    flex: 1;
    overflow: hidden;
}

.input-container {
    flex-shrink: 0;
}
</style>
