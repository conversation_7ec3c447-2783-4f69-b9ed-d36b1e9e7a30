<template>
    <div class="message-list" ref="messageListRef">
        <!-- 欢迎消息 -->
        <WelcomeMessage v-if="messages.length === 0 && !isGenerating" />

        <!-- 消息列表 -->
        <div
            v-for="message in messages"
            :key="message.id"
            class="message-wrapper"
        >
            <MessageItem :message="message" />
        </div>

        <!-- 加载指示器 -->
        <div v-if="isGenerating" class="loading-message">
            <div class="message-avatar assistant">
                <vscode-progress-ring></vscode-progress-ring>
            </div>
            <div class="message-content loading">
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue';
import type { Message } from '../types';
import MessageItem from './MessageItem.vue';
import WelcomeMessage from './WelcomeMessage.vue';

// 导入vscode-elements组件
import '@vscode-elements/elements/dist/vscode-progress-ring';

interface Props {
    messages: Message[];
    isGenerating: boolean;
}

const props = defineProps<Props>();
const messageListRef = ref<HTMLElement>();

// 滚动到底部
const scrollToBottom = () => {
    nextTick(() => {
        if (messageListRef.value) {
            messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
        }
    });
};

// 监听消息变化，自动滚动到底部
watch(
    () => props.messages.length,
    () => {
        scrollToBottom();
    }
);

watch(
    () => props.isGenerating,
    () => {
        scrollToBottom();
    }
);
</script>

<style scoped>
.message-list {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    scroll-behavior: smooth;
}

.message-wrapper {
    margin-bottom: 16px;
}

.loading-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.message-avatar.assistant {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.message-content.loading {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 80%;
}

.loading-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--vscode-foreground);
    opacity: 0.4;
    animation: loading-pulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading-pulse {
    0%,
    80%,
    100% {
        opacity: 0.4;
        transform: scale(1);
    }
    40% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 滚动条样式 */
.message-list::-webkit-scrollbar {
    width: 8px;
}

.message-list::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.message-list::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}
</style>
