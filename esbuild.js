const esbuild = require('esbuild');
const vue3Plugin = require('esbuild-plugin-vue3');

const production = process.argv.includes('--production');
const watch = process.argv.includes('--watch');

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
    name: 'esbuild-problem-matcher',

    setup(build) {
        build.onStart(() => {
            console.log('[watch] build started');
        });
        build.onEnd((result) => {
            result.errors.forEach(({ text, location }) => {
                console.error(`✘ [ERROR] ${text}`);
                console.error(
                    `    ${location.file}:${location.line}:${location.column}:`
                );
            });
            console.log('[watch] build finished');
        });
    }
};

async function main() {
    // Build extension
    const extensionCtx = await esbuild.context({
        entryPoints: ['src/extension.ts'],
        bundle: true,
        format: 'cjs',
        minify: production,
        sourcemap: !production,
        sourcesContent: false,
        platform: 'node',
        outfile: 'dist/extension.js',
        external: ['vscode'],
        logLevel: 'silent',
        plugins: [
            /* add to the end of plugins array */
            esbuildProblemMatcherPlugin
        ]
    });

    // Build webview (Vue app)
    const webviewCtx = await esbuild.context({
        entryPoints: ['src/webview/main.ts'],
        bundle: true,
        format: 'iife',
        minify: production,
        sourcemap: !production,
        sourcesContent: false,
        platform: 'browser',
        outfile: 'dist/webview.js',
        logLevel: 'silent',
        loader: {
            '.css': 'css'
        },
        plugins: [vue3Plugin(), esbuildProblemMatcherPlugin]
    });
    if (watch) {
        await extensionCtx.watch();
        await webviewCtx.watch();
    } else {
        await extensionCtx.rebuild();
        await webviewCtx.rebuild();
        await extensionCtx.dispose();
        await webviewCtx.dispose();
    }
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
